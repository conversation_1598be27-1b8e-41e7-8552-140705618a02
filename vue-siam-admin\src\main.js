import babelpolyfill from 'babel-polyfill'
import Vue from 'vue'
import App from './App.vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import './common/common.css'
import VueRouter from 'vue-router'
import store from './vuex/store'
import Vuex from 'vuex'
import myPrint from './components/internal/print'
import orderPrint from './components/internal/orderPrint'
import VCharts from 'v-charts'

// import VueQuillEditor from 'vue-quill-editor'
// import {ImageDrop} from 'quill-image-drop-module'

// import ImageResize from 'quill-image-resize-module'

// import 'quill/dist/quill.core.css'
// import 'quill/dist/quill.snow.css'
// import 'quill/dist/quill.bubble.css'

// Quill.register('modules/imageDrop', ImageDrop)
// Quill.register('modules/imageResize', ImageResize)


import http from './utils/http';
import utils from './utils/common';
Vue.prototype.$http = http;
Vue.prototype.$utils = utils;
import routes from './routes'
import VueCropper from 'vue-cropper'

Vue.use(ElementUI)
Vue.use(VueRouter)
Vue.use(Vuex)
Vue.use(myPrint)
Vue.use(orderPrint)
Vue.use(VCharts)
Vue.use(VueCropper)
// Vue.use(VueQuillEditor)

// 全局指令：处理图片加载失败
Vue.directive('img-error', {
  bind(el, binding) {
    // 保存原始的src和尺寸
    const originalSrc = el.src
    const width = el.width || el.style.width || '50px'
    const height = el.height || el.style.height || '50px'
    
    el.addEventListener('error', function() {
      // 移除src属性避免重复触发error事件
      el.removeAttribute('src')
      // 添加错误样式类
      el.classList.add('img-error')
      // 设置尺寸
      el.style.width = width
      el.style.height = height
      el.style.minWidth = width
      el.style.minHeight = height
    })
    
    // 如果有自定义错误处理函数，也执行它
    if (binding.value && typeof binding.value === 'function') {
      el.addEventListener('error', binding.value)
    }
  }
})


const router = new VueRouter({
  routes
})

router.beforeEach((to, from, next) => {
  let user = JSON.parse(sessionStorage.getItem('user'));
  if (to.path == '/login' || to.path == '/QuickLogin' || to.path == '/setPassword') {
    next()
  }else if (!user && to.path != '/login') {
    sessionStorage.removeItem('user');
    next({ path: '/login' })
  } else if(to.path == '/') {
    next({path: '/statisticGraph'})
  } else {
    next()
  }
})


new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
}).$mount('#app')

