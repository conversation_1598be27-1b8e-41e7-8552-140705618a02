<template>
	<section>
		<!--工具条-->
		<el-col :span="24" class="toolbar" style="padding-bottom: 2px;">
			<el-form :inline="true" :model="searchMsg">
				<!-- <el-form-item label="城市" prop="city">
					<el-input v-model="searchMsg.city" clearable placeholder="请输入销城市格"></el-input>
				</el-form-item> -->
				<!-- <el-form-item label="充值后是否赠送优惠券" prop="isGiveridercity">
					<el-select v-model="searchMsg.isGiveridercity" clearable>
						<el-option label="固定" :value="1"></el-option>
						<el-option label="按比例" :value="2"></el-option>
						 <el-option label="满减优惠券" :value="2"></el-option> 
					</el-select>
				</el-form-item> -->
				<!-- <el-form-item>
					<el-button type="primary" @click="getList(1)">查询</el-button>
				</el-form-item> -->
				<el-form-item>
					<el-button type="primary" @click="handleEditDiscount">新增</el-button>
					<!-- <el-button type="primary" @click="handleEditFullReduction">新增满减优惠券</el-button> -->
				</el-form-item>
			</el-form>
		</el-col>
		<!--列表-->
		<el-table :data="list" highlight-current-row v-loading="listLoading" style="width: 100%;" :cell-style="cellStyle" :header-cell-style="headerCellStyle">
			<!-- <el-table-column type="index" label="序号" width="50">
				<template scope="scope">
					<span>{{(searchMsg.pageNo - 1) * searchMsg.pageSize + scope.$index + 1}}</span>
				</template>		
			</el-table-column> -->
			<!-- <el-table-column prop="id" label="会员编号"></el-table-column> -->
				<el-table-column prop="province" label="省份"></el-table-column>	
			<el-table-column prop="city" label="城市"></el-table-column>
			<el-table-column prop="area" label="区/县" ></el-table-column>
			<el-table-column prop="street" label="街道" ></el-table-column>	
	  <el-table-column prop="deliveryStartingDistance" label="配送起步距离" width="190">
			<template scope="scope">
				<span >{{ scope.row.deliveryStartingDistance }}KM</span>
			</template>
		</el-table-column>	
	  <el-table-column prop="deliveryStartingPrice" label="配送起步价" width="190">
			<template scope="scope">
				<span >{{ scope.row.deliveryStartingPrice }}元</span>
			</template>
		</el-table-column>	
	  <el-table-column prop="deliveryKilometerPrice" label="配送公里价" width="190">
			<template scope="scope">
				<span >{{ scope.row.deliveryKilometerPrice }}元/KM</span>
			</template>
		</el-table-column>	
	  <el-table-column prop="deliveryDistanceLimit" label="配送距离上限" width="190">
			<template scope="scope">
				<span >{{ scope.row.deliveryDistanceLimit }}KM</span>
			</template>
		</el-table-column>	
	  <el-table-column prop="nearbyRidersDistanceLimit" label="附近骑手-显示距离取件地、服务地多少公里内的骑手" width="190">
			<template scope="scope">
				<span >{{ scope.row.nearbyRidersDistanceLimit }}KM</span>
			</template>
		</el-table-column>
			<el-table-column prop="createTime" label="创建时间" :formatter="formatTime"></el-table-column> 
			<el-table-column prop="updateTime" label="修改时间" :formatter="formatTime"></el-table-column> 
			<!-- <el-table-column prop="createTime" label="创建时间" :formatter="formatValidType1" width="190"></el-table-column>
			<el-table-column prop="updateTime" label="	修改时间" :formatter="formatValidType2"></el-table-column> -->
						
			<el-table-column label="操作" fixed="right" width="260">
				<template slot-scope="scope"> 
					<el-button size="small"  @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
					<el-button type="danger"  size="small" @click="handleDel(scope.row.id)">删除</el-button>
				
					<el-button size="small"  v-if="scope.row.isGiveridercity=='1'"  @click="handleRelationShop(scope.row.id)">关联赠送优惠券</el-button>
		
					<!-- <el-button size="small" @click="handleGiveridercity(scope.row.id)">群发优惠券</el-button> -->
				</template>
				<!-- scope.$index, scope.row -->
			</el-table-column>
		</el-table>

		<!--工具条-->
		<el-col :span="24" class="toolbar">
			<el-pagination
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="searchMsg.pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				style="float:right;">
			</el-pagination>
		</el-col>		

		<!--编辑界面-会有充值-->
		<el-dialog title="编辑" :visible.sync="editFormVisible" @close="closeDialog" :close-on-click-modal="false">
			<el-form :model="editForm" label-width="210px" style="width: 80%;" :rules="editFormRules" ref="editForm">
				<el-form-item label="省份" prop="province">
					<el-input v-model="editForm.province"></el-input>
				</el-form-item>	
				<el-form-item label="城市" prop="city">
					<el-input v-model="editForm.city"></el-input>
				</el-form-item>

				<el-form-item label="区/县" prop="area" >
					<el-input v-model="editForm.area"></el-input>
				</el-form-item>
				
				<el-form-item label="街道" prop="street" >
					<el-input v-model="editForm.street"></el-input>
				</el-form-item>

				<el-form-item label="配送起步距离(KM)" prop="deliveryStartingDistance">
					<el-input type="number" v-model="editForm.deliveryStartingDistance"></el-input>
				</el-form-item>						 

				<el-form-item label="配送起步价(元)" prop="deliveryStartingPrice">
					<el-input type="number" v-model="editForm.deliveryStartingPrice"></el-input>
				</el-form-item>		

				<el-form-item label="配送公里价(元/KM)" prop="deliveryKilometerPrice">
					<el-input type="number" v-model="editForm.deliveryKilometerPrice"></el-input>
				</el-form-item>						 

				<el-form-item label="配送距离上限(KM)" prop="deliveryDistanceLimit">
					<el-input type="number" v-model="editForm.deliveryDistanceLimit"></el-input>
				</el-form-item>						 

				<el-form-item label="附近骑手-显示距离取件地、服务地多少公里内的骑手" prop="nearbyRidersDistanceLimit">
					<el-input type="number" v-model="editForm.nearbyRidersDistanceLimit"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click.native="editFormVisible = false">取消</el-button>
				<el-button type="primary" @click.native="editSubmit" :loading="editLoading">提交</el-button>
			</div>
		</el-dialog>
		
	</section>
</template>

<script>
	export default {
		data() {
			return {
				searchMsg: {
					pageNo: 1,
					pageSize: 20
				},
				
				list: [],
				total: 2,
				listLoading: false,
				sels: [],//列表选中列

				editFormVisible: false,//编辑界面是否显示
				editLoading: false,

				editarea:false, //区/县是否显示


				editFormVisibleFullReduction: false,//编辑界面是否显示
				editLoadingFullReduction: false,

				editFormRules: {
					city: [{ required: true, message: '请输入城市', trigger: 'blur' }],
					//isGiveBalance: [{ required: true, message: '请选择充值后是否赠送余额', trigger: 'blur' }],
					//giveBalance: [{ required: true, message: '请输入赠送余额数量', trigger: 'blur' }],
					isGiveridercity: [{ required: true, message: '请选择是否赠送优惠券', trigger: 'blur' }],
					// deliveryFeeRatio: [{ required: true, message: '请输入配送费比例(%)', trigger: 'blur' }],
					province: [{ required: true, message: '请输入省份', trigger: 'blur' }],
					//settlementMode: [{ required: true, message: '请选择分成模式', trigger: 'blur' }],
					// deliveryFeeRatio: [{ required: true, message: '请输入配送费比例(%)', trigger: 'blur' }],
					// perOrderFixedAmount: [{ required: true, message: '请输入每单固定金额', trigger: 'blur' }],
					validEndTime: [{ required: false, message: '请选择使用结束时间', trigger: 'blur' }],
					validDays: [{ required: false, message: '请输入自领取之日起有效天数', trigger: 'blur' }],
				},				
				//编辑界面数据
				editForm: {
					province: '',
					preferentialType: '',
					discountAmount: '',
					limitedcity: '',
					reducedcity: '',
					deliveryFeeRatio: '',
					validType: '',
					validStartTime: '',
					validEndTime: '',
					settlementMode: '固定',
				},

				editFormVisibleShopTable: false,//编辑界面是否显示
				editLoadingShopTable: false,
				shopTable: '',
				multipleSelection: [],
				//关联店铺的列表
				searchMsgShopTable: {
					pageNo: -1,
					pageNoIndex: 1,
					pageSize: 20
				},		
				listShopTable: [],
				totalShopTable: 2,
				listLoadingShopTable: false,
				selsShopTable: [],//列表选中列			
				ridercityIdShopTable: '' ,	
				consumedQuantityRelationTable: [] ,		
			}
		},
		methods: {
			cellStyle({row, column, rowIndex, columnIndex}){
				return "text-align:center";
			},
			headerCellStyle({row, rowIndex}){
				return "text-align:center";
			},			
			closeDialog() {
				this.$refs['editForm'].resetFields();
			},
			formatTime(row, column) {
				let date = new Date(row[column.property]);
				let str = this.$utils.formatDate(date, 'yyyy-MM-dd hh:mm:ss');
				// 处理数据库中时间为NULL场景
				if(row[column.property]==undefined || str=="1970-01-01 08:00:00"){
					str = "-";
				}
        		return str;
			},
			formatValidType1(row, column) {
				if(row.validType == 1){
					let date = new Date(row[column.property]);
					let str = this.$utils.formatDate(date, 'yyyy-MM-dd hh:mm:ss');
					// 处理数据库中时间为NULL场景
					if(str == "1970-01-01"){
						str = "-";
					}
					return str;					
				}else{
					return "-"
				}				
			},	
			chooseisshow(row, column){
				if (row.settlementMode==1){
					this.editarea=true	; //区/县是否显示

				}else{

					this.editarea=false;
				}
				},
			formatValidType2(row, column) {
				if(row.validType == 2){
					return row.validDays + "天";
				}else{
					return "-"
				}	
			},						
			formatType (row, column) { // 0=正常/启用  1=禁用
				return row.status == 1 ? '开启' : '关闭';
			},
			addUnit(row, column) { // 添加单位
				return (row[column.property] || 0) + '元'
			},					
			formatPreferentialType1(row, column) { // 添加单位
				if(row.preferentialType == 1){
					return (row[column.property] * 10) + '折'
				}else{
					return "-"
				}
			},
			formatPreferentialType2(row, column) { // 添加单位
				if(row.preferentialType == 2){
					return (row[column.property] || 0) + '元'
				}else{
					return "-"
				}
			},			
			// formatarea(row, column) { // 添加单位
			// 	if(row.settlementMode){
			// 		return row.area + '元';
			// 	}else{
			// 		return "-";
			// 	}
			// },
			handleSizeChange(val) {
				this.searchMsg.pageSize = val;
				this.getList();
			},			   
			handleCurrentChange(val) {
				this.searchMsg.pageNo = val;
				this.getList();
			},
			getList(pageNoParam) { // 获取列表
				if(pageNoParam){
				this.searchMsg.pageNo = pageNoParam;
				} // 查询会员等级列表
				let vue = this
				let param = Object.assign(vue.searchMsg);
				vue.listLoading = true;

				//查询未被删除的商品
				//param.isDelete = 0;
				//param.source = 2;

				vue.$http.post(vue, '/api-rider/rest/admin/deliveryAreaConfig/page', param,
					(vue, data) => {
						vue.list = data.data.records
						vue.total = data.data.total
						vue.listLoading = false;
					},(error, data)=> {
						alert(data);
						vue.listLoading = false;
						vue.$message({
							showClose: true,
							message: data.message,
							type: 'error'
						});
					}
				)
			},
			handleDel (id) { // 删除
				this.$confirm('确认删除该记录吗?', '提示', {
					type: 'warning'
				}).then(() => {
					// this.listLoading = true;
					let vue = this;
				
					vue.$http.post(vue, '/api-rider/rest/admin/deliveryAreaConfig/delete?id='+id, {"id" : id},
						function(vue, data) {
							vue.$message({
								showClose: true,
								message: data.message,
								type: 'success'
							});
							vue.getList()
						}, function(error, data) {
							vue.$message({
								showClose: true,
								message: data.message,
								type: 'error'
							});
						}
					)
				}).catch(() => {});
			},
			// handleGiveridercity (id) { // 派发优惠券
			// 		let vue = this;
			// 		vue.$http.post(vue, '/api-rider/rest/admin/deliveryAreaConfigMemberRelation/giveridercity', {"ridercityId" : id},
			// 			function(vue, data) {
			// 				vue.$message({
			// 					showClose: true,
			// 					message: data.message,
			// 					type: 'success'
			// 				});
			// 				vue.getList()
			// 			}, function(error, data) {
			// 				vue.$message({
			// 					showClose: true,
			// 					message: data.message,
			// 					type: 'error'
			// 				});
			// 			}
			// 		)

			// 	// vue.$http.post(vue, url, param,
			// 	// 	(vue, data) => {
			// 	// 		// this.editLoadingShopTable = false;
			// 	// 		vue.$message({
			// 	// 			showClose: true,
			// 	// 			message: data.message,
			// 	// 			type: 'success'
			// 	// 		});
			// 	// 		// vue.getList()
			// 	// 		vue.editFormVisibleShopTable = false;						
			// 	// 	}, (error, data) => {
			// 	// 		vue.editFormVisibleShopTable = false;
			// 	// 		vue.$message({
			// 	// 			showClose: true,
			// 	// 			message: data.message,
			// 	// 			type: 'error'
			// 	// 		});
			// 	// 	}
			// 	// )							
			// },			
			handleEdit: function (index, row) { // 显示编辑界面
				// if(row.preferentialType == 1){
					 this.editFormVisible = true;
				// }else{
					//  this.editFormVisibleFullReduction = true;
				// }
			
				if(row){
					this.editForm = {
						id: row.id,
                        province: row.province,
						city: row.city,
						area: row.area,
						street: row.street,
						deliveryStartingDistance: row.deliveryStartingDistance,
						deliveryStartingPrice: row.deliveryStartingPrice,
						deliveryKilometerPrice: row.deliveryKilometerPrice,
						deliveryDistanceLimit: row.deliveryDistanceLimit,
						nearbyRidersDistanceLimit: row.nearbyRidersDistanceLimit,
					}
				}else{
					this.editForm = {
						province: '',
						city: '',
						area: '',
						street: '',
						deliveryStartingDistance: '',
						deliveryStartingPrice: '',
						deliveryKilometerPrice: '',
						deliveryDistanceLimit: '',
						nearbyRidersDistanceLimit: ''
					}
				}					
			},
			handleEditDiscount: function (index, row) { // 显示编辑界面
				this.editFormVisible = true;
				if(row){
					this.editForm = {
						id: row.id,
						province: row.province,
						preferentialType: row.preferentialType == 1 ? '折扣' : '满减',
						discountAmount: row.preferentialType == 1 ? row.discountAmount : '',
						limitedcity: row.preferentialType == 2 ? row.limitedcity : '',
						reducedcity: row.preferentialType == 2 ? row.reducedcity : '',
						deliveryFeeRatio: row.deliveryFeeRatio,
						validType: row.validType == 1 ? '绝对时效' : '相对时效',
						validStartTime: row.validType == 1 ? new Date(row.validStartTime).getTime() : '',
						validEndTime: row.validType == 1 ? new Date(row.validEndTime).getTime() : '',
						validDays: row.validType == 2 ? row.validDays : '',
					}
				}else{
					this.editForm = {
						province: '',
						preferentialType: '',
						discountAmount: '',
						limitedcity: '',
						reducedcity: '',
						deliveryFeeRatio: '',
						validType: '',
						validStartTime: '',
						validEndTime: '',
						validDays: '',
						settlementMode:'按比例',
						isGiveBalance:'按比例',
					}
				}					
			},
			handleEditFullReduction: function (index, row) { // 显示编辑界面
				this.editFormVisibleFullReduction = true;
				if(row){
					this.editForm = {
						id: row.id,
						province: row.province,
						preferentialType: row.preferentialType == 1 ? '折扣' : '满减',
						discountAmount: row.preferentialType == 1 ? row.discountAmount : '',
						limitedcity: row.preferentialType == 2 ? row.limitedcity : '',
						reducedcity: row.preferentialType == 2 ? row.reducedcity : '',
						deliveryFeeRatio: row.deliveryFeeRatio,
						validType: row.validType == 1 ? '绝对时效' : '相对时效',
						validStartTime: row.validType == 1 ? new Date(row.validStartTime).getTime() : '',
						validEndTime: row.validType == 1 ? new Date(row.validEndTime).getTime() : '',
						validDays: row.validType == 2 ? row.validDays : '',
					}
				}else{
					this.editForm = {
						province: '',
						preferentialType: '',
						discountAmount: '',
						limitedcity: '',
						reducedcity: '',
						deliveryFeeRatio: '',
						validType: '',
						validStartTime: '',
						validEndTime: '',
						validDays: '',
					}
				}					
			},						
			editSubmit: function () { // 编辑
				this.$refs.editForm.validate((valid) => {
					if (valid) {						
						let vue = this
						let param = Object.assign({}, this.editForm);
						let url = '';

						//优惠券发放来源为调度中心
						//param.source = 2;

						//param.preferentialType = 1;

					param.isGiveBalance = (param.isGiveBalance == '固定') ? 1 : 2;	
						param.isGiveridercity = (param.isGiveridercity == '固定') ? 1 : 2;
							param.settlementMode = (param.settlementMode == '固定') ? 1 : 2;
						// param.preferentialType = (param.preferentialType == '折扣') ? 1 : 2;
						// param.validType = (param.validType == '绝对时效') ? 1 : 2;	
						// if(param.city == ''){
						// 	vue.$message({
						// 	showClose: true,
						// 	message: '请填写折扣额度',
						// 	type: 'error'
						// 	});
						// 	return false							
						// }			
						// if(param.discountAmount<0 || param.discountAmount>=1){
						// 	vue.$message({
						// 	showClose: true,
						// 	message: '请填写正确的折扣额度',
						// 	type: 'error'
						// 	});
						// 	return false							
						// }									
						// delete param.limitedcity;
						// delete param.reducedcity;			

						// //判断优惠类型合法性
						// if(param.preferentialType == 1){
						// 	if(param.discountAmount == ''){
						// 		vue.$message({
						// 		showClose: true,
						// 		message: '请填写折扣额度',
						// 		type: 'error'
						// 		});
						// 		return false							
						// 	}			
						// 	if(param.discountAmount<0 || param.discountAmount>=1){
						// 		vue.$message({
						// 		showClose: true,
						// 		message: '请填写正确的折扣额度',
						// 		type: 'error'
						// 		});
						// 		return false							
						// 	}									
						// 	delete param.limitedcity;
						// 	delete param.reducedcity;
						// }else if(param.preferentialType == 2){
						// 	if(param.limitedcity == ''){
						// 		vue.$message({
						// 		showClose: true,
						// 		message: '请填写满足额度',
						// 		type: 'error'
						// 		});
						// 		return false							
						// 	}									
						// 	if(param.reducedcity == ''){
						// 		vue.$message({
						// 		showClose: true,
						// 		message: '请填写减价额度',
						// 		type: 'error'
						// 		});
						// 		return false							
						// 	}							
						// 	if(parseFloat(param.reducedcity) >= parseFloat(param.limitedcity)){
						// 		vue.$message({
						// 		showClose: true,
						// 		message: '减价额度必须小于满足城市',
						// 		type: 'error'
						// 		});
						// 		return false							
						// 	}		
						// 	delete param.discountAmount;						
						// }

						//判断时效合法性
						// if(param.validType == 1){
						// 	if(param.validStartTime == ''){
						// 		vue.$message({
						// 		showClose: true,
						// 		message: '请填写使用开始时间',
						// 		type: 'error'
						// 		});
						// 		return false							
						// 	}									
						// 	if(param.validEndTime == ''){
						// 		vue.$message({
						// 		showClose: true,
						// 		message: '请填写使用结束时间',
						// 		type: 'error'
						// 		});
						// 		return false							
						// 	}							
						// 	let end = new Date(param.validEndTime);
						// 	end.setHours(0);
						// 	end.setMinutes(0);
						// 	end.setSeconds(0);
						// 	let start = new Date(param.validStartTime);
						// 	start.setHours(0);
						// 	start.setMinutes(0);
						// 	start.setSeconds(0);								
						// 	if(end <= start){
						// 		vue.$message({
						// 		showClose: true,
						// 		message: '使用结束时间必须大于使用开始时间',
						// 		type: 'error'
						// 		});
						// 		return false							
						// 	}						
						// 	delete param.validDays;			
						// 	//处理下时间格式(本来传递一个时间戳就可以了的，但是后台配置有问题，所以先用字符串传参)
						// 	let validStartTime = new Date(param.validStartTime);
						// 	let str = this.$utils.formatDate(validStartTime, 'yyyy/MM/dd');
						// 	param.validStartTime = str;

						// 	let validEndTime = new Date(param.validEndTime);
						// 	str = this.$utils.formatDate(validEndTime, 'yyyy/MM/dd');
						// 	param.validEndTime = str;										
						// }else if(param.validType == 2){
						// 	if(param.validDays == ''){
						// 		vue.$message({
						// 		showClose: true,
						// 		message: '请填写有效天数',
						// 		type: 'error'
						// 		});
						// 		return false							
						// 	}	
						// 	delete param.validStartTime;				
						// 	delete param.validEndTime;				
						// }						
				
						// this.editLoading = true;			

						if(param.id){
							url = '/api-rider/rest/admin/deliveryAreaConfig/update';
							vue.$http.post(vue, url, param,
								(vue, data) => {
									// this.editLoading = false;
									vue.$message({
										showClose: true,
										message: data.message,
										type: 'success'
									});
									
									vue.getList()
									vue.editFormVisible = false;
								}, (error, data) => {
									vue.editFormVisible = false;
									vue.$message({
										showClose: true,
										message: data.message,
										type: 'error'
									});
								}
							)							
						}else{
							url = '/api-rider/rest/admin/deliveryAreaConfig/insert';
							vue.$http.post(vue, url, param,
								(vue, data) => {
									// this.editLoading = false;
									vue.$message({
										showClose: true,
										message: data.message,
										type: 'success'
									});
									
									vue.getList()
									vue.editFormVisible = false;
								}, (error, data) => {
									vue.editFormVisible = false;
									vue.$message({
										showClose: true,
										message: data.message,
										type: 'error'
									});
								}
							)							
						}
					}
				});
			},			
			editSubmitFullReduction: function () { // 编辑
				this.$refs.editForm.validate((valid) => {
					if (valid) {						
						let vue = this
						let param = Object.assign({}, this.editForm);
						let url = '';

						//优惠券发放来源为调度中心
						param.source = 2;

						param.preferentialType = 2;
						// param.preferentialType = (param.preferentialType == '折扣') ? 1 : 2;
						param.validType = (param.validType == '绝对时效') ? 1 : 2;	
						if(param.limitedcity == ''){
							vue.$message({
							showClose: true,
							message: '请填写满足额度',
							type: 'error'
							});
							return false							
						}									
						if(param.reducedcity == ''){
							vue.$message({
							showClose: true,
							message: '请填写减价额度',
							type: 'error'
							});
							return false							
						}							
						if(parseFloat(param.reducedcity) >= parseFloat(param.limitedcity)){
							vue.$message({
							showClose: true,
							message: '减价额度必须小于满足城市',
							type: 'error'
							});
							return false							
						}		
						delete param.discountAmount;

						//判断优惠类型合法性
						// if(param.preferentialType == 1){
						// 	if(param.discountAmount == ''){
						// 		vue.$message({
						// 		showClose: true,
						// 		message: '请填写折扣额度',
						// 		type: 'error'
						// 		});
						// 		return false							
						// 	}			
						// 	if(param.discountAmount<0 || param.discountAmount>=1){
						// 		vue.$message({
						// 		showClose: true,
						// 		message: '请填写正确的折扣额度',
						// 		type: 'error'
						// 		});
						// 		return false							
						// 	}									
						// 	delete param.limitedcity;
						// 	delete param.reducedcity;
						// }else if(param.preferentialType == 2){
						// 	if(param.limitedcity == ''){
						// 		vue.$message({
						// 		showClose: true,
						// 		message: '请填写满足额度',
						// 		type: 'error'
						// 		});
						// 		return false							
						// 	}									
						// 	if(param.reducedcity == ''){
						// 		vue.$message({
						// 		showClose: true,
						// 		message: '请填写减价额度',
						// 		type: 'error'
						// 		});
						// 		return false							
						// 	}							
						// 	if(parseFloat(param.reducedcity) >= parseFloat(param.limitedcity)){
						// 		vue.$message({
						// 		showClose: true,
						// 		message: '减价额度必须小于满足城市',
						// 		type: 'error'
						// 		});
						// 		return false							
						// 	}		
						// 	delete param.discountAmount;						
						// }

						//判断时效合法性
						if(param.validType == 1){
							if(param.validStartTime == ''){
								vue.$message({
								showClose: true,
								message: '请填写使用开始时间',
								type: 'error'
								});
								return false							
							}									
							if(param.validEndTime == ''){
								vue.$message({
								showClose: true,
								message: '请填写使用结束时间',
								type: 'error'
								});
								return false							
							}							
							let end = new Date(param.validEndTime);
							end.setHours(0);
							end.setMinutes(0);
							end.setSeconds(0);
							let start = new Date(param.validStartTime);
							start.setHours(0);
							start.setMinutes(0);
							start.setSeconds(0);								
							if(end <= start){
								vue.$message({
								showClose: true,
								message: '使用结束时间必须大于使用开始时间',
								type: 'error'
								});
								return false							
							}						
							delete param.validDays;			
							//处理下时间格式(本来传递一个时间戳就可以了的，但是后台配置有问题，所以先用字符串传参)
							let validStartTime = new Date(param.validStartTime);
							let str = this.$utils.formatDate(validStartTime, 'yyyy/MM/dd');
							param.validStartTime = str;

							let validEndTime = new Date(param.validEndTime);
							str = this.$utils.formatDate(validEndTime, 'yyyy/MM/dd');
							param.validEndTime = str;										
						}else if(param.validType == 2){
							if(param.validDays == ''){
								vue.$message({
								showClose: true,
								message: '请填写有效天数',
								type: 'error'
								});
								return false							
							}	
							delete param.validStartTime;				
							delete param.validEndTime;				
						}						
				
						// this.editLoadingFullReduction = true;			

						if(param.id){
							url = '/api-rider/rest/admin/deliveryAreaConfig/update';
							vue.$http.post(vue, url, param,
								(vue, data) => {
									// this.editLoadingFullReduction = false;
									vue.$message({
										showClose: true,
										message: data.message,
										type: 'success'
									});
									
									vue.getList()
									vue.editFormVisibleFullReduction = false;
								}, (error, data) => {
									vue.editFormVisibleFullReduction = false;
									vue.$message({
										showClose: true,
										message: data.message,
										type: 'error'
									});
								}
							)							
						}else{
							url = '/api-rider/rest/admin/deliveryAreaConfig/insert';
							vue.$http.post(vue, url, param,
								(vue, data) => {
									// this.editLoadingFullReduction = false;
									vue.$message({
										showClose: true,
										message: data.message,
										type: 'success'
									});
									
									vue.getList()
									vue.editFormVisibleFullReduction = false;
								}, (error, data) => {
									vue.editFormVisibleFullReduction = false;
									vue.$message({
										showClose: true,
										message: data.message,
										type: 'error'
									});
								}
							)							
						}
					}
				});
			},					
			getListShopTable() { // 获取优惠劵列表
				let vue = this
        		let param = Object.assign({}, vue.searchMsgShopTable, {"token":sessionStorage.getItem("token")});

				console.log("param="+JSON.stringify(param));
				console.log("searchMsg="+JSON.stringify(vue.searchMsgShopTable));

				vue.listLoadingShopTable = true;
				vue.$http.post(vue, '/api-rider/rest/admin/deliveryAreaConfig/page', param,
					(vue, data) => {
						vue.listShopTable = data.data.records
						vue.totalShopTable = data.data.total
						vue.listLoadingShopTable = false;
						//this.$refs.moviesTable.toggleRowSelection(row)
					},(error, data)=> {
						vue.listLoadingShopTable = false;
						vue.$message({
							showClose: true,
							message: data.message,
							type: 'error'
						});
					}
				)		
			},				
			handleRelationShop (id) { // 关联赠送优惠劵
				this.$nextTick(function () {
					//清除之前选中的数据，如果不加nextTick就会报错，但是下面放在循环里面或者按钮出发的就不会报错
					this.toggleSelectionSingle();	
				})		
				//清除表格中的耗量输入框数据
				this.consumedQuantityRelationTable = [];	
				this.editFormVisibleShopTable = true;
				this.ridercityIdShopTable = id;
				let vue = this
                let param1 = Object.assign(vue.searchMsg);
				param1.ridercityId=id
				
				vue.$http.post(vue, '/api-rider/rest/admin/deliveryAreaConfigridercityRelation/list',param1,
					(vue, data) => {
						let shopList = data.data.records				
						//alert(shopList.length);
						if(shopList.length > 0){
							//默认选中已经关联的优惠劵
							for(let i = 0; i < shopList.length; i++){
								for(let j = 0; j < this.listShopTable.length; j++){
									if(shopList[i].ridercityId == this.listShopTable[j].id){
										this.toggleSelectionSingle(this.listShopTable[j])
										this.consumedQuantityRelationTable[shopList[i].ridercityId] = shopList[i].giveQuantity;
										break;
									}									
								}							
							}
						}			
						console.log(this.multipleSelection);
					},(error, data)=> {
						vue.$message({
							showClose: true,
							message: data.message,
							type: 'error'
						});
					}
				)			
			},		
			editSubmitShopTable: function () { // 关联会员保存事件
				let vue = this
				let param = {
					ridercityId : this.ridercityIdShopTable
					//denominationridercityRelationListStr : ''
				};
				let url = '';

				if(this.multipleSelection.length == 2){
					vue.$message({
						showClose: true,
						message: '请选择要关联的优惠劵',
						type: 'error'
					});
					return false							
				}		

				//生成商品id数组
				let shopIdList = [];
				for(let i = 0; i < this.multipleSelection.length; i++){
					//shopIdList[i] = this.multipleSelection[i].id;									
					shopIdList.push(this.multipleSelection[i].id);			
				}

				
				//param.shopIdListStr = JSON.stringify(shopIdList);

				//不用这种拼接的方式
				// let str = '';
				// for(let i = 0; i < this.multipleSelection.length; i++){
				// 	str += this.multipleSelection[i].id + ",";
				// }
				// str = str.substring(0, str.length-1);
				// param.shopIdList = str;
		 
				//生成商品原料关联列表
				let relationList = [];
				for(let i = 0; i < this.multipleSelection.length; i++){
					// alert(this.consumedQuantityRelationTable[this.multipleSelection[i].id])
					let consumedQuantity = this.consumedQuantityRelationTable[this.multipleSelection[i].id];
					if(consumedQuantity==undefined || consumedQuantity==""){
						vue.$message({
							showClose: true,
							message: '请填写' + this.multipleSelection[i].province + '奖励数量',
							type: 'error'
						});
						return false							
					}
					let map = {
						"ridercityId" : this.multipleSelection[i].id,
						"giveQuantity" : consumedQuantity
					}
					relationList.push(map);
				}
				param.denominationridercityRelationListStr = JSON.stringify(relationList);

				
				
				// this.editLoadingShopTable = true;
			
				url = '/api-rider/rest/admin/deliveryAreaConfigridercityRelation/insert';
				vue.$http.post(vue, url, param,
					(vue, data) => {
						// this.editLoadingShopTable = false;
						vue.$message({
							showClose: true,
							message: data.message,
							type: 'success'
						});
						
						// vue.getList()
						vue.editFormVisibleShopTable = false;						
					}, (error, data) => {
						vue.editFormVisibleShopTable = false;
						vue.$message({
							showClose: true,
							message: data.message,
							type: 'error'
						});
							
					}
				)							
			},							
			// toggleSelection() {
			// 	let vue = this;
			// 	// vue.$refs.table.clearSelection();
			// 	vue.listShopTable.forEach((item) => {
			// 		//设置该表格选框选中
			// 		// vue.$refs.table.toggleRowSelection(item);
			// 	});				
				
			// },			
			toggleSelection(rows) {
				if (rows) {
					rows.forEach(row => {
						this.$refs.shopTable.toggleRowSelection(row);
					});
				} else {
					this.$refs.shopTable.clearSelection();
				}
			},
			toggleSelectionSingle(row) {
				if (row) {
					this.$refs.shopTable.toggleRowSelection(row);
				} else {
					this.$refs.shopTable.clearSelection();
				}
			},										
			handleSelectionChange(val) {
				this.multipleSelection = val;
				console.log(this.multipleSelection);
			}					
		},
		mounted() {
			this.getList();
			this.getListShopTable();
            //开启订单自动打印定时器
            this.$orderPrint.init();			
		}
	}

</script>

<style scoped>
  /* .el-button+.el-button {
    margin-top: 10px;
    margin-left: 2;
  } */
  .dialogDiv {
      height: 560px;
      overflow-y: auto;
      overflow-x: hidden;
  }
</style>