<template>
  <section>
    <el-form :model="editForm" label-width="150px" class="editForm" style="width: 80%;" :rules="editFormRules" ref="editForm">
				<el-form-item label="模板名称" prop="templateName">
					<el-input v-model="editForm.templateName" clearable placeholder="模板名称"></el-input>
				</el-form-item>
				<el-form-item label="模版内容" prop="templateContent">
					<el-input v-model="editForm.templateContent" type="textarea" :rows="5"  clearable placeholder="模版内容"></el-input>
				</el-form-item>
				<el-form-item label="模版code" prop="templateCode">
					<el-input v-model="editForm.templateCode" clearable placeholder="模版code"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="el-dialog__footer">
				<el-button @click="goBack">取消</el-button>
				<el-button type="primary" @click="saveGoodsMsg">保存</el-button>
			</div>
  </section>
</template>
<script>
export default {
  data() {
    return {
      menuList: [],
      editForm: {
		templateName: '',
		templateContent: '',
		templateCode: '', 
      },
      editFormRules: {
        templateName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
        templateContent: [{ required: true, message: '请输入模板内容', trigger: 'blur' }],
		templateCode: [{ required: true, message: '请输入模板code', trigger: 'blur' }],
      }
    }
  },
  methods: {
    saveGoodsMsg() {
		this.$refs.editForm.validate((valid) => {
			if (valid) {		  						
				this.editLoading = true;
				let vue = this

				let param = Object.assign({}, this.editForm);
				let url = '';
				url = '/api-user/api/sysSmsTemplate/insert';
				vue.$http.post(vue, url, param,
					(vue, data) => {
						this.editLoading = false;
						vue.$message({
							showClose: true,
							message: data.message,
							type: 'success'
						});
						vue.goBack()
						vue.editFormVisible = false;
					}, (error, data) => {
						vue.editFormVisible = false;
						vue.$message({
							showClose: true,
							message: data.message,
							type: 'error'
						});
					}
				)							
			}
		});
    },
    goBack() {
      this.$refs['editForm'].resetFields();
      this.$router.push({path: 'smsConfig'})
    },	
  },
  created() {
    this.editForm.status = 1;
    //开启订单自动打印定时器
    this.$orderPrint.init();         
  }
}
</script>
<style scoped>
.editForm .el-input {
  width: 420px;
}
.standForm .el-input {
  width: 200px;
}
.avatar-uploader .el-upload {
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 148px;
    height: 148px;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 148px;
    height: 148px;
    line-height: 148px;
    text-align: center;
  }
  .avatar {
    width: 148px;
    height: 148px;
    display: block;
  }
  .editForm .minInput {
    width: 240px;
  }
</style>

