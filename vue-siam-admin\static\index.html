<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>易达脉联 - 官网首页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 头部导航 */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 0px 40px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            transition: opacity 0.3s ease;
        }

        .logo:hover {
            opacity: 0.8;
        }

        .logo img {
            height: 50px;
            width: auto;
            object-fit: contain;
        }

        .logo-text {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
            margin-left: 10px;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #3498db;
        }

        /* 主图区域 */
        .hero {
            padding: 100px 0 50px;
            background: #f8f9fa;
            text-align: center;
        }

        .hero-image {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .hero-content h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .hero-content p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #7f8c8d;
        }

        .cta-button {
            display: inline-block;
            padding: 12px 30px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .cta-button:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        /* 横幅轮播区域 */
        .banner-section {
            padding: 4rem 0;
            background: white;
        }

        .carousel-container {
            position: relative;
            max-width: 1000px;
            margin: 2rem auto;
            overflow: hidden;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .carousel-wrapper {
            display: flex;
            transition: transform 0.5s ease-in-out;
        }

        .carousel-slide {
            min-width: 100%;
            position: relative;
        }

        .carousel-slide img {
            width: 100%;
            height: 400px;
            object-fit: cover;
            display: block;
        }

        .slide-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            color: white;
            padding: 3rem 2rem 2rem;
        }

        .slide-overlay h3 {
            font-size: 1.8rem;
            margin-bottom: 0.8rem;
        }

        .slide-overlay p {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        /* 轮播控制按钮 */
        .carousel-btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            color: #333;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .carousel-btn:hover {
            background: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .carousel-btn.prev {
            left: 20px;
        }

        .carousel-btn.next {
            right: 20px;
        }

        /* 轮播指示器 */
        .carousel-indicators {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ddd;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .indicator.active {
            background: #3498db;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-content h1 {
                font-size: 2.5rem;
            }

            .nav-links {
                display: none;
            }

            .carousel-slide img {
                height: 250px;
            }

            .slide-overlay {
                padding: 2rem 1rem 1rem;
            }

            .slide-overlay h3 {
                font-size: 1.4rem;
            }

            .slide-overlay p {
                font-size: 0.9rem;
            }

            .carousel-btn {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
        }

        /* 滚动动画 */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <nav class="nav container">
            <a href="https://spa.hnydml.com/shop" class="logo" target="_blank">
                <img src="./images/logo.jpg" alt="易达脉联 Logo">
                <span class="logo-text">易达脉联</span>
            </a>
            <ul class="nav-links">
                <li><a href="#home">首页</a></li>
                <li><a href="#services">服务</a></li>
                <li><a href="#about">关于我们</a></li>
                <li><a href="#contact">联系我们</a></li>
            </ul>
        </nav>
    </header>

    <!-- 主图区域 -->
    <section class="hero" id="home">
        <div class="container">
            <img src="./images/main.jpg" alt="易达脉联 主图" class="hero-image">
            <div class="hero-content">
                <h1>欢迎来到 易达脉联</h1>
                <p>专业的云服务解决方案，助力您的业务腾飞</p>
                <a href="#services" class="cta-button">了解更多</a>
            </div>
        </div>
    </section>

    <!-- 横幅轮播区域 -->
    <section class="banner-section" id="services">
        <div class="container">
            <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 1rem; color: #2c3e50;">我们的服务</h2>
            <p style="text-align: center; font-size: 1.1rem; color: #7f8c8d; margin-bottom: 3rem;">为您提供全方位的云服务解决方案</p>

            <div class="carousel-container fade-in">
                <div class="carousel-wrapper" id="carouselWrapper">
                    <div class="carousel-slide">
                        <img src="./images/banner1.jpg" alt="云计算服务">
                        <div class="slide-overlay">
                            <h3>云计算服务</h3>
                            <p>高性能、高可用的云计算基础设施，为您的应用提供稳定可靠的运行环境，支持弹性扩展和负载均衡</p>
                        </div>
                    </div>

                    <div class="carousel-slide">
                        <img src="./images/banner2.jpg" alt="数据存储">
                        <div class="slide-overlay">
                            <h3>数据存储解决方案</h3>
                            <p>安全可靠的数据存储服务，支持多种存储类型，满足不同业务需求，提供数据备份和恢复功能</p>
                        </div>
                    </div>

                    <div class="carousel-slide">
                        <img src="./images/banner3.jpg" alt="技术支持">
                        <div class="slide-overlay">
                            <h3>专业技术支持</h3>
                            <p>7x24小时专业技术支持，确保您的业务持续稳定运行，提供快速响应和问题解决方案</p>
                        </div>
                    </div>
                </div>

                <button class="carousel-btn prev" onclick="prevSlide()">‹</button>
                <button class="carousel-btn next" onclick="nextSlide()">›</button>

                <div class="carousel-indicators">
                    <span class="indicator active" onclick="currentSlide(1)"></span>
                    <span class="indicator" onclick="currentSlide(2)"></span>
                    <span class="indicator" onclick="currentSlide(3)"></span>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer style="background: #2c3e50; color: white; text-align: center; padding: 2rem 0;">
        <div class="container">
            <p>&copy; 2024 易达脉联. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // 轮播图功能
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.carousel-slide');
        const indicators = document.querySelectorAll('.indicator');
        const wrapper = document.getElementById('carouselWrapper');
        const totalSlides = slides.length;

        function showSlide(index) {
            currentSlideIndex = index;
            wrapper.style.transform = `translateX(-${index * 100}%)`;

            // 更新指示器
            indicators.forEach((indicator, i) => {
                indicator.classList.toggle('active', i === index);
            });
        }

        function nextSlide() {
            currentSlideIndex = (currentSlideIndex + 1) % totalSlides;
            showSlide(currentSlideIndex);
        }

        function prevSlide() {
            currentSlideIndex = (currentSlideIndex - 1 + totalSlides) % totalSlides;
            showSlide(currentSlideIndex);
        }

        function currentSlide(index) {
            showSlide(index - 1);
        }

        // 自动轮播
        setInterval(nextSlide, 5000);

        // 滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
