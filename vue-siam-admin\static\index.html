<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SIAM Cloud - 官网首页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 头部导航 */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #3498db;
        }

        /* 主图区域 */
        .hero {
            height: 100vh;
            background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('./images/main.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
        }

        .hero-content h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .hero-content p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .cta-button {
            display: inline-block;
            padding: 12px 30px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .cta-button:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        /* 横幅区域 */
        .banner-section {
            padding: 4rem 0;
            background: #f8f9fa;
        }

        .banner-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .banner-item {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .banner-item:hover {
            transform: translateY(-5px);
        }

        .banner-item img {
            width: 100%;
            height: 250px;
            object-fit: cover;
        }

        .banner-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            color: white;
            padding: 2rem 1.5rem 1.5rem;
        }

        .banner-overlay h3 {
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
        }

        .banner-overlay p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-content h1 {
                font-size: 2.5rem;
            }

            .nav-links {
                display: none;
            }

            .banner-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 滚动动画 */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <nav class="nav container">
            <div class="logo">SIAM Cloud</div>
            <ul class="nav-links">
                <li><a href="#home">首页</a></li>
                <li><a href="#services">服务</a></li>
                <li><a href="#about">关于我们</a></li>
                <li><a href="#contact">联系我们</a></li>
            </ul>
        </nav>
    </header>

    <!-- 主图区域 -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1>欢迎来到 SIAM Cloud</h1>
            <p>专业的云服务解决方案，助力您的业务腾飞</p>
            <a href="#services" class="cta-button">了解更多</a>
        </div>
    </section>

    <!-- 横幅区域 -->
    <section class="banner-section" id="services">
        <div class="container">
            <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 1rem; color: #2c3e50;">我们的服务</h2>
            <p style="text-align: center; font-size: 1.1rem; color: #7f8c8d; margin-bottom: 3rem;">为您提供全方位的云服务解决方案</p>
            
            <div class="banner-grid fade-in">
                <div class="banner-item">
                    <img src="./images/banner1.jpg" alt="云计算服务">
                    <div class="banner-overlay">
                        <h3>云计算服务</h3>
                        <p>高性能、高可用的云计算基础设施，为您的应用提供稳定可靠的运行环境</p>
                    </div>
                </div>
                
                <div class="banner-item">
                    <img src="./images/banner2.jpg" alt="数据存储">
                    <div class="banner-overlay">
                        <h3>数据存储解决方案</h3>
                        <p>安全可靠的数据存储服务，支持多种存储类型，满足不同业务需求</p>
                    </div>
                </div>
                
                <div class="banner-item">
                    <img src="./images/banner3.jpg" alt="技术支持">
                    <div class="banner-overlay">
                        <h3>专业技术支持</h3>
                        <p>7x24小时专业技术支持，确保您的业务持续稳定运行</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer style="background: #2c3e50; color: white; text-align: center; padding: 2rem 0;">
        <div class="container">
            <p>&copy; 2024 SIAM Cloud. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // 滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
